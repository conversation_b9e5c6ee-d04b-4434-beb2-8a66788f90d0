package ai.friday.dda.app.tenant

enum class TenantProtocol {
    HTTP, MESSAGE
}

sealed interface ResponseConfiguration {
    data class HttpConfiguration(val username: String, val password: String) : ResponseConfiguration
    data class MessageConfiguration(val queueName: String) : ResponseConfiguration
}

enum class TenantName {
    FRIDAY,
    MOTOROLA
}

interface TenantConfiguration {
    val tenantName: TenantName
    val bucket: String
    val protocol: TenantProtocol
    val configuration: ResponseConfiguration
}

fun List<TenantConfiguration>.getResponseConfigurationByTenant(tenantName: TenantName): ResponseConfiguration? {
    return firstOrNull { it.tenantName == tenantName }?.configuration
}