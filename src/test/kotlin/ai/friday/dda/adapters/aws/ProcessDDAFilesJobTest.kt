package ai.friday.dda.adapters.aws

import ai.friday.dda.adapters.arbi.DDABillsBatchProcessor
import ai.friday.dda.adapters.arbi.DDAFilterService
import ai.friday.dda.adapters.arbi.SendMessageProcessor
import ai.friday.dda.adapters.jobs.ProcessDDAFilesJob
import ai.friday.dda.app.BrazilZonedDateTimeSupplier
import ai.friday.dda.app.interfaces.MessagePublisher
import ai.friday.dda.app.interfaces.ObjectRepository
import ai.friday.dda.app.tenant.TenantConfiguration
import io.kotest.core.spec.style.AnnotationSpec
import io.micronaut.test.extensions.kotest.annotation.MicronautTest
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.io.FileInputStream
import java.time.format.DateTimeFormatter
import utils.MicronautPropertiesTest

@MicronautTest
@MicronautPropertiesTest
class ProcessDDAFilesJobTest : AnnotationSpec() {
    private lateinit var test: ProcessDDAFilesJob

    private lateinit var objectRepository: ObjectRepository
    private lateinit var messagePublisher: MessagePublisher
    private lateinit var processor: S3FileProcessor

    private val bucketName = "fake-bucket"
    private val today = BrazilZonedDateTimeSupplier.getLocalDate()
    private val s3FolderName = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"))
    private val ddaFilterServiceMock: DDAFilterService = mockk {
        every { filter(any()) } returns true
    }
    private val tenantConfiguration = mockk<TenantConfiguration>()
    private val sendMessageProcessor = mockk<SendMessageProcessor>()

    @BeforeEach
    fun setUp() {
        this.objectRepository = mockk(relaxed = true)
        this.messagePublisher = mockk(relaxed = true)
        this.processor = mockk(relaxed = true)

        this.test = ProcessDDAFilesJob(
            DDABillsBatchProcessor(ddaFilterServiceMock, sendMessageProcessor),
            processor,
            tenantConfiguration
        )
    }

    @Test
    fun `nao deve processar nada`() {
        every { objectRepository.listObjectKeys(bucketName, "downloaded") } returns listOf()

        test.run()

        verify { objectRepository.listObjectKeys(bucketName, "downloaded") }
        verify { objectRepository wasNot called }
    }

    @Test
    fun `deve mover o arquivo para a pasta de processados`() {
        every {
            objectRepository.listObjectKeys(bucketName, directoryKey = "downloaded")
        } returns listOf("downloaded/object_key.json.gz")

        every {
            objectRepository.loadObject(bucketName, "downloaded/object_key.json.gz")
        } answers {
            val resource = Thread.currentThread().contextClassLoader.getResource("files/DDAInformation.json.gz")
            FileInputStream(resource!!.path)
        }

        test.run()

        verify(exactly = 1) {
            objectRepository.listObjectKeys(bucketName, "downloaded")
            messagePublisher.sendMessageBatch(any())
            objectRepository.moveObject(
                bucketName,
                "downloaded/object_key.json.gz",
                "processed/$s3FolderName/object_key.json.gz"
            )
        }
    }

    @Test
    fun `deve mover o arquivo para a pasta de erros se houver uma excecao no processamento`() {
        every {
            objectRepository.listObjectKeys(bucketName, "downloaded")
        } returns listOf("downloaded/object_key.json.gz")

        every {
            objectRepository.loadObject(bucketName, "downloaded/object_key.json.gz")
        } answers {
            val resource = Thread.currentThread().contextClassLoader.getResource("files/Invalid_File.json.gz")
            FileInputStream(resource!!.path)
        }

        test.run()

        verify(exactly = 1) {
            objectRepository.moveObject(bucketName, "downloaded/object_key.json.gz", "errors/object_key.json.gz")
        }
    }
}