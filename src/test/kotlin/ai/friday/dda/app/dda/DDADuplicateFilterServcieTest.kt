package ai.friday.dda.app.dda

import ai.friday.dda.adapters.arbi.DDABillTO
import ai.friday.dda.adapters.arbi.DDADuplicateFilterService
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.KotlinFeature
import com.fasterxml.jackson.module.kotlin.KotlinModule
import io.kotest.core.spec.IsolationMode
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.lettuce.core.api.StatefulRedisConnection
import io.lettuce.core.api.sync.RedisCommands
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.io.FileInputStream
import java.time.Duration

class DDADuplicateFilterServcieTest : DescribeSpec() {
    override fun isolationMode() = IsolationMode.InstancePerTest

    init {
        val resource = Thread.currentThread().contextClassLoader.getResource("files/DDAInformation.json")
        val mapper = ObjectMapper()
            .registerModule(
                KotlinModule.Builder()
                    .configure(KotlinFeature.NullIsSameAsDefault, enabled = true)
                    .build()
            )

        val ddaList: List<DDABillTO> = mapper.readValue<List<DDABillTO>?>(
            FileInputStream(resource!!.path),
            mapper.typeFactory.constructCollectionType(List::class.java, DDABillTO::class.java)
        )
        val dda = ddaList.first().copy(
            numidentcdda = 123,
            ultnumrefcadtit = 456,
            ultnumseqcadtit = 7
        )

        val redisCommandMock: RedisCommands<String, String?> = mockk() {
            every { expire(any(), any<Duration>()) } returns true
        }
        val redisMock: StatefulRedisConnection<String, String?> = mockk {
            every { sync() } returns redisCommandMock
        }

        val service = DDADuplicateFilterService(redisMock)

        describe("ao filtrar dda") {
            describe("quando o registro já existir no redis") {
                every { redisCommandMock.getset(any(), any()) } answers { secondArg() }

                it("deveria retornar false") {
                    val result = service.filter(dda)

                    result shouldBe false

                    verify(exactly = 1) {
                        redisCommandMock.getset("DDA_BILL#fdb5539cedec6785dc0473c7842d6cb2", mapper.writeValueAsString(dda))
                    }
                }
            }

            describe("quando o registro ainda não existir no redis") {
                every { redisCommandMock.getset(any(), any()) } returns null
                every { redisCommandMock.expire(any(), any<Duration>()) } returns true

                val result = service.filter(dda)

                it("deveria retornar true") {
                    result shouldBe true

                    verify(exactly = 1) {
                        redisCommandMock.getset("DDA_BILL#fdb5539cedec6785dc0473c7842d6cb2", mapper.writeValueAsString(dda))
                    }
                }

                it("deveria setar expiração do registro no redis") {
                    verify(exactly = 1) {
                        redisCommandMock.expire("DDA_BILL#fdb5539cedec6785dc0473c7842d6cb2", Duration.ofDays(1))
                    }
                }
            }
        }
    }
}