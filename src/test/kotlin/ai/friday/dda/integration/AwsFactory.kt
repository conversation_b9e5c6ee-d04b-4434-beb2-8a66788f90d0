package ai.friday.dda.integration

import ai.friday.dda.DynamoDBUtils
import io.micronaut.context.annotation.Factory
import io.micronaut.context.annotation.Replaces
import io.mockk.mockk
import jakarta.inject.Singleton
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.sqs.SqsClient

@Factory
class AwsFactory {
    @Factory
    class DynamoFactory {
        @Singleton
        fun clis() = DynamoDBUtils.setup()

        @Singleton
        @Replaces(bean = DynamoDbClient::class)
        fun customDynamo(clis: DynamoDBUtils.DynamoClis) = clis.syncCli
    }

    @Factory
    class SqsFactory {
        @Singleton
        @Replaces(bean = SqsClient::class)
        fun sqs() = mockk<SqsClient>(relaxed = true, relaxUnitFun = true)
    }
}